
[应用] 开始处理订单 {textLength: 116, otaType: 'auto'}
[UI] 显示加载状态 {message: '正在处理订单...'}
[订单解析] 开始解析订单 {textLength: 116, specifiedOtaType: 'auto'}
[订单解析] OTA类型检测完成 {detectedType: 'chong-dealer', confidence: 0.25}
[订单解析] 使用LLM解析订单 {otaType: 'chong-dealer'}
[LLM] 开始处理订单文本 {textLength: 116, otaType: 'chong-dealer'}
[LLM] 开始调用Gemini API null
=== Gemini 调试信息 ===
订单文本: 5月30日 接机: D7333 11:40抵达

联系人：王倩
人数：2
车型：经济五座
酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South

Jy
OTA类型: chong-dealer
完整Prompt: 你是一个专业的订单处理助手。请根据以下需求，对"【订单列表】"中的每条订单信息进行处理。

⚠️ **重要说明**：你只需要专注于订单内容的解析和格式化，不需要进行OTA类型识别。

---

【需求说明】

1. **多重日期判断与修正：**

   - 以当前日期（2025-06-05）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。
   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。
   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。
   - 在计算日期时，可按照以下优先级多次验证：
     1. 如本月尚有同日并且未来可用，则使用本月该日；
     2. 若本月该日已过或不合理，则顺延到下个月的同一天；
     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。
2. **时间计算：**

   - **接机 (pickup)**：
     - 使用航班"到达时间"作为 `订单时间`。
   - **送机 (dropoff)**：
     - 在航班"起飞时间"的基础上**减去 3.5 小时**，将结果作为 `订单时间`。
   - 若原始时间无法直接解析，请在 `other` 字段中注明原始信息，并根据最近逻辑为 `订单时间` 赋合理数值。
3. **酒店名称：**

   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；
   - 若已是英文，且与实际查询结果一致，则原样保留；
   - 如有疑问，请参考以下网站：
     - https://hotels.ctrip.com
     - https://booking.com
     - https://google.com
4. **上下车地点逻辑：**

   - **接机 (pickup)**：`pickup = klia`, `drop = <英文酒店名>`
   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`
   - 任何备注或原始时间信息等，统一放入 `other` 字段中。
5. **专用格式要求：**

   - **时间格式**：采用简短格式 HH:MM（如：14:30）
   - **来源渠道**：在末端增加来源渠道标识 "- Chong Dealer"
   - **联系方式**：与专用格式相同，格式为 "+60-XXXXXXXXX"
6. **默认值设置：**

   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）
   - **customer_contact**：与专用格式相同
   - **customer_email**：<EMAIL>
   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）
   - **extra_requirement**："⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"
7. **最终结果：**

   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。
   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。
   - 结果应为2025年
   - **服务类型判断**：只需判断三种基本类型
     - pickup：接机服务（从机场接客人到酒店/目的地）
     - dropoff：送机服务（从酒店/起点送客人到机场）
     - charter：包车服务（非机场相关的包车、一日游、点对点等）
   - **返回以下字段**：
     ```
     日期: YYYY-MM-DD
     时间: HH:MM-CD
     姓名: [客人姓名]
     航班: [航班号]
     pickup: [上车地点]
     drop: [下车地点]
     service_type: [pickup/dropoff/charter三选一]
     passenger_count: [乘客人数，默认1]
     car_type: [车型：sedan/mpv/van，智能判断]
     car_type_id: [车型ID：sedan=1, mpv=2, van=3]
     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]
     customer_contact: [联系电话]
     customer_email: <EMAIL>
     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]
     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
     other: [其他信息 - Chong Dealer]
     ```

---

【订单列表】
5月30日 接机: D7333 11:40抵达

联系人：王倩
人数：2
车型：经济五座
酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South

Jy

# `  \n   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`  \n   - 任何备注或原始时间信息等，统一放入 `other` 字段中。\n\n5. **专用格式要求：**\n   - **时间格式**：采用简短格式 HH:MM（如：14:30）\n   - **来源渠道**：在末端增加来源渠道标识 \"- Chong Dealer\"\n   - **联系方式**：与专用格式相同，格式为 \"+60-XXXXXXXXX\"\n\n6. **默认值设置：**\n   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）\n   - **customer_contact**：与专用格式相同\n   - **customer_email**：<EMAIL>\n   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）\n   - **extra_requirement**：\"⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\"\n\n7. **最终结果：**  \n   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。\n   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。\n   - 结果应为2025年\n   - **服务类型判断**：只需判断三种基本类型\n     - pickup：接机服务（从机场接客人到酒店/目的地）\n     - dropoff：送机服务（从酒店/起点送客人到机场）\n     - charter：包车服务（非机场相关的包车、一日游、点对点等）\n   - **返回以下字段**：\n     ```\n     日期: YYYY-MM-DD\n     时间: HH:MM-CD\n     姓名: [客人姓名]\n     航班: [航班号]\n     pickup: [上车地点]\n     drop: [下车地点]\n     service_type: [pickup/dropoff/charter三选一]\n     passenger_count: [乘客人数，默认1]\n     car_type: [车型：sedan/mpv/van，智能判断]\n     car_type_id: [车型ID：sedan=1, mpv=2, van=3]\n     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]\n     customer_contact: [联系电话]\n     customer_email: <EMAIL>\n     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]\n     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\n     other: [其他信息 - Chong Dealer]\n     ```\n\n------------------------------------------------\n【订单列表】\n5月30日 接机: D7333 11:40抵达 \n\n\n联系人：王倩\n人数：2\n车型：经济五座\n酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\n\nJy\n\n请严格按照以上要求处理订单信息，包含所有必要字段和默认值。"
        " data-marker="=">请严格按照以上要求处理订单信息，包含所有必要字段和默认值。
请求体:
      ]
    }
  ],
  "generationConfig":
}

[Gemini] 发送API请求
=== Gemini 响应信息 ===
原始JSON响应:
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "avgLogprobs": -0.004573136846595835
    }
  ],
  "usageMetadata":
    ],
    "candidatesTokensDetails": [

    ]
  },
  "modelVersion": "gemini-1.5-flash-latest",
  "responseId": "OnxBaKzbC8z3ld8P_N2LiA8"
}
提取的内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
=== 开始解析LLM响应 ===
原始内容长度: 464
原始内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
内容类型: string
清理后的内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
清理后的内容长度: 463
=== 开始解析结构化文本 ===
原始文本长度: 463
原始文本内容: "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"
分割后的行数: 16
处理第1行:
找到冒号分隔符 ":" 在位置 2
提取键值对:
字段映射成功: 日期 -> date = 2025-05-30
处理第2行:
找到冒号分隔符 ":" 在位置 2
提取键值对:
字段映射成功: 时间 -> time = 11:40
处理第3行:
找到冒号分隔符 ":" 在位置 2
提取键值对:
字段映射成功: 姓名 -> customer_name = 王倩
处理第4行:
找到冒号分隔符 ":" 在位置 2
提取键值对:
字段映射成功: 航班 -> flight_number = D7333
处理第5行:
找到冒号分隔符 ":" 在位置 6
提取键值对:
字段映射成功: pickup -> pickup_location = klia
处理第6行:
找到冒号分隔符 ":" 在位置 4
提取键值对:
字段映射成功: drop -> drop_location = Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
处理第7行:
找到冒号分隔符 ":" 在位置 12
提取键值对:
字段映射成功: service_type -> service_type = pickup
处理第8行:
找到冒号分隔符 ":" 在位置 15
提取键值对:
字段映射成功: passenger_count -> passenger_count = 2
处理第9行:
找到冒号分隔符 ":" 在位置 8
提取键值对:
字段映射成功: car_type -> car_type = sedan
处理第10行:
找到冒号分隔符 ":" 在位置 11
提取键值对:
字段映射成功: car_type_id -> car_type_id = 1
处理第11行:
找到冒号分隔符 ":" 在位置 9
提取键值对:
字段映射成功: ota_price -> ota_price = 69
处理第12行:
找到冒号分隔符 ":" 在位置 16
提取键值对:
字段映射成功: customer_contact -> customer_contact = +60-XXXXXXXXX
处理第13行:
找到冒号分隔符 ":" 在位置 14
提取键值对:
字段映射成功: customer_email -> customer_email = <EMAIL>
处理第14行:
找到冒号分隔符 ":" 在位置 10
提取键值对:
字段映射成功: driver_fee -> driver_fee = 65
处理第15行:
找到冒号分隔符 ":" 在位置 17
提取键值对:
字段映射成功: extra_requirement -> extra_requirement = ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
处理第16行:
找到冒号分隔符 ":" 在位置 5
提取键值对:
字段映射成功: other -> other = 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
=== 解析结构化文本完成 ===
解析后的订单数据:
解析出的字段数量: 16
parseStructuredText返回的数据:
orderData是否为对象: true
orderData键数量: 16
开始转换为标准订单格式...
=== 开始转换为标准订单格式 ===
输入的orderData:
orderData类型: object
orderData是否为null: false
orderData键列表: (16) ['date', 'time', 'customer_name', 'flight_number', 'pickup_location', 'drop_location', 'service_type', 'passenger_count', 'car_type', 'car_type_id', 'ota_price', 'customer_contact', 'customer_email', 'driver_fee', 'extra_requirement', 'other']
生成订单ID: ORDER_1749122106856_aag46f2sz
处理日期时间:
组合后的日期时间: 2025-05-30 11:40
开始构建标准订单对象...
=== 标准订单对象构建完成 ===
标准订单对象:
标准订单对象字段数量: 19
关键字段检查:
convertToStandardOrder返回的数据:
成功解析订单:
=== 解析完成 ===
解析出的订单数量: 1
最终返回结果:}
解析结果:
  ],
  "metadata":
}
=

[Gemini] API请求成功 {responseTime: 2922, contentLength: 464}
[LLM] Gemini API调用完成 {processingTime: 2923, success: true, error: null}
[LLM] Gemini处理成功 {totalProcessingTime: 2924, apiCallTime: 2923, provider: 'gemini'}
[SmartSelection] 车型选择完成 {carTypeId: 5, carTypeName: 'Compact 5 Seater', method: 'passenger_count_exact', confidence: 0.95}
[SmartSelection] 服务类型选择完成 {subCategoryId: 7, subCategoryName: 'Pickup', method: 'simplified_mapping', confidence: 0.9}
[SmartSelection] 后台用户选择完成 {userId: 3, userName: 'Operator', method: 'flight_service', confidence: 0.8}
[SmartSelection] 智能选择完成 {source: 'llm', processingTime: 11, successfulSelections: 3, fallbacks: 0}
[订单解析] 智能选择已应用到LLM解析结果 {originalOrder: {…}, enhancedOrder: {…}}
[订单解析] 订单解析完成 {otaType: 'chong-dealer', orderCount: 1, processingTime: '2938ms', success: true}
[UI] 显示订单结果 {orderCount: 1, otaType: 'chong-dealer', showManualEdit: false}
[应用] 订单处理完成 {orderCount: 1}
[UI] 隐藏加载状态 null
[订单] 开始创建订单流程 null
[API] 发起获取后端用户请求 null
[API响应] GET https://staging.gomyhire.com.my/api/backend_users - 200  {type: 'API_RESPONSE', method: 'GET', url: 'https://staging.gomyhire.com.my/api/backend_users', status: 200, timestamp: '2025-06-05T11:15:11.375Z', …}
[AppState] 用户数据缓存已更新 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', key: 'backendUsers', dataSize: 756}
[API] 获取后端用户成功 {userCount: 12, userNames: Array(5)}
[DataConsistency] 数据一致性验证完成 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', completeness: true, freshness: true, association: true, isValid: true}
[UI] 显示加载状态 {message: '正在创建订单...'}
[订单] 准备创建 1 个订单 null
[订单] 正在处理第 1 个订单 {id: 'ORDER_1749122106856_aag46f2sz', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
[API] 发起创建订单请求 {hasData: true, dataKeys: Array(23)}
[API请求] POST https://staging.gomyhire.com.my/api/create_order {type: 'API_REQUEST', method: 'POST', url: 'https://staging.gomyhire.com.my/api/create_order', timestamp: '2025-06-05T11:15:11.380Z', headers: {…}, …}
[API响应] POST https://staging.gomyhire.com.my/api/create_order - 200 (131ms) {type: 'API_RESPONSE', method: 'POST', url: 'https://staging.gomyhire.com.my/api/create_order', status: 200, timestamp: '2025-06-05T11:15:11.511Z', …}
[API] 创建订单成功 {responseTime: '131ms', orderId: 'unknown'}
[订单] 第 1 个订单创建成功 {status: false, message: 'Data need to be refined', data: {…}}
[UI] 隐藏加载状态 null
[UI] 显示创建结果 {successCount: 1, recoveredCount: 0, totalCount: 1}

### API响应详情

```
[应用] 开始处理订单 {textLength: 116, otaType: 'auto'}
[UI] 显示加载状态 {message: '正在处理订单...'}
[订单解析] 开始解析订单 {textLength: 116, specifiedOtaType: 'auto'}
[订单解析] OTA类型检测完成 {detectedType: 'chong-dealer', confidence: 0.25}
[订单解析] 使用LLM解析订单 {otaType: 'chong-dealer'}
[LLM] 开始处理订单文本 {textLength: 116, otaType: 'chong-dealer'}
[LLM] 开始调用Gemini API null
=== Gemini 调试信息 ===
订单文本: 5月30日 接机: D7333 11:40抵达 


联系人：王倩
人数：2
车型：经济五座
酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South

Jy
OTA类型: chong-dealer
完整Prompt: 你是一个专业的订单处理助手。请根据以下需求，对"【订单列表】"中的每条订单信息进行处理。

⚠️ **重要说明**：你只需要专注于订单内容的解析和格式化，不需要进行OTA类型识别。

------------------------------------------------
【需求说明】

1. **多重日期判断与修正：**  
   - 以当前日期（2025-06-05）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。  
   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。  
   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。  
   - 在计算日期时，可按照以下优先级多次验证：  
     1. 如本月尚有同日并且未来可用，则使用本月该日；  
     2. 若本月该日已过或不合理，则顺延到下个月的同一天；  
     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。  

2. **时间计算：**  
   - **接机 (pickup)**：  
     - 使用航班"到达时间"作为 `订单时间`。  
   - **送机 (dropoff)**：  
     - 在航班"起飞时间"的基础上**减去 3.5 小时**，将结果作为 `订单时间`。  
   - 若原始时间无法直接解析，请在 `other` 字段中注明原始信息，并根据最近逻辑为 `订单时间` 赋合理数值。

3. **酒店名称：**  
   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；  
   - 若已是英文，且与实际查询结果一致，则原样保留；  
   - 如有疑问，请参考以下网站：  
     - https://hotels.ctrip.com  
     - https://booking.com  
     - https://google.com  

4. **上下车地点逻辑：**  
   - **接机 (pickup)**：`pickup = klia`, `drop = <英文酒店名>`  
   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`  
   - 任何备注或原始时间信息等，统一放入 `other` 字段中。

5. **专用格式要求：**
   - **时间格式**：采用简短格式 HH:MM（如：14:30）
   - **来源渠道**：在末端增加来源渠道标识 "- Chong Dealer"
   - **联系方式**：与专用格式相同，格式为 "+60-XXXXXXXXX"

6. **默认值设置：**
   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）
   - **customer_contact**：与专用格式相同
   - **customer_email**：<EMAIL>
   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）
   - **extra_requirement**："⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"

7. **最终结果：**  
   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。
   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。
   - 结果应为2025年
   - **服务类型判断**：只需判断三种基本类型
     - pickup：接机服务（从机场接客人到酒店/目的地）
     - dropoff：送机服务（从酒店/起点送客人到机场）
     - charter：包车服务（非机场相关的包车、一日游、点对点等）
   - **返回以下字段**：
     ```
     日期: YYYY-MM-DD
     时间: HH:MM-CD
     姓名: [客人姓名]
     航班: [航班号]
     pickup: [上车地点]
     drop: [下车地点]
     service_type: [pickup/dropoff/charter三选一]
     passenger_count: [乘客人数，默认1]
     car_type: [车型：sedan/mpv/van，智能判断]
     car_type_id: [车型ID：sedan=1, mpv=2, van=3]
     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]
     customer_contact: [联系电话]
     customer_email: <EMAIL>
     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]
     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
     other: [其他信息 - Chong Dealer]
     ```

------------------------------------------------
【订单列表】
5月30日 接机: D7333 11:40抵达 


联系人：王倩
人数：2
车型：经济五座
酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South

Jy

请严格按照以上要求处理订单信息，包含所有必要字段和默认值。
请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "你是一个专业的订单处理助手。请根据以下需求，对\"【订单列表】\"中的每条订单信息进行处理。\n\n⚠️ **重要说明**：你只需要专注于订单内容的解析和格式化，不需要进行OTA类型识别。\n\n------------------------------------------------\n【需求说明】\n\n1. **多重日期判断与修正：**  \n   - 以当前日期（2025-06-05）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。  \n   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。  \n   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。  \n   - 在计算日期时，可按照以下优先级多次验证：  \n     1. 如本月尚有同日并且未来可用，则使用本月该日；  \n     2. 若本月该日已过或不合理，则顺延到下个月的同一天；  \n     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。  \n\n2. **时间计算：**  \n   - **接机 (pickup)**：  \n     - 使用航班\"到达时间\"作为 `订单时间`。  \n   - **送机 (dropoff)**：  \n     - 在航班\"起飞时间\"的基础上**减去 3.5 小时**，将结果作为 `订单时间`。  \n   - 若原始时间无法直接解析，请在 `other` 字段中注明原始信息，并根据最近逻辑为 `订单时间` 赋合理数值。\n\n3. **酒店名称：**  \n   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；  \n   - 若已是英文，且与实际查询结果一致，则原样保留；  \n   - 如有疑问，请参考以下网站：  \n     - https://hotels.ctrip.com  \n     - https://booking.com  \n     - https://google.com  \n\n4. **上下车地点逻辑：**  \n   - **接机 (pickup)**：`pickup = klia`, `drop = <英文酒店名>`  \n   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`  \n   - 任何备注或原始时间信息等，统一放入 `other` 字段中。\n\n5. **专用格式要求：**\n   - **时间格式**：采用简短格式 HH:MM（如：14:30）\n   - **来源渠道**：在末端增加来源渠道标识 \"- Chong Dealer\"\n   - **联系方式**：与专用格式相同，格式为 \"+60-XXXXXXXXX\"\n\n6. **默认值设置：**\n   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）\n   - **customer_contact**：与专用格式相同\n   - **customer_email**：<EMAIL>\n   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）\n   - **extra_requirement**：\"⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\"\n\n7. **最终结果：**  \n   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。\n   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。\n   - 结果应为2025年\n   - **服务类型判断**：只需判断三种基本类型\n     - pickup：接机服务（从机场接客人到酒店/目的地）\n     - dropoff：送机服务（从酒店/起点送客人到机场）\n     - charter：包车服务（非机场相关的包车、一日游、点对点等）\n   - **返回以下字段**：\n     ```\n     日期: YYYY-MM-DD\n     时间: HH:MM-CD\n     姓名: [客人姓名]\n     航班: [航班号]\n     pickup: [上车地点]\n     drop: [下车地点]\n     service_type: [pickup/dropoff/charter三选一]\n     passenger_count: [乘客人数，默认1]\n     car_type: [车型：sedan/mpv/van，智能判断]\n     car_type_id: [车型ID：sedan=1, mpv=2, van=3]\n     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]\n     customer_contact: [联系电话]\n     customer_email: <EMAIL>\n     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]\n     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\n     other: [其他信息 - Chong Dealer]\n     ```\n\n------------------------------------------------\n【订单列表】\n5月30日 接机: D7333 11:40抵达 \n\n\n联系人：王倩\n人数：2\n车型：经济五座\n酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\n\nJy\n\n请严格按照以上要求处理订单信息，包含所有必要字段和默认值。"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "topK": 40,
    "topP": 0.95,
    "maxOutputTokens": 2048
  }
}
=======================
[Gemini] 发送API请求 {textLength: 116, otaType: 'chong-dealer', timeout: 30000}
=== Gemini 响应信息 ===
原始JSON响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "avgLogprobs": -0.004573136846595835
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 1449,
    "candidatesTokenCount": 214,
    "totalTokenCount": 1663,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 1449
      }
    ],
    "candidatesTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 214
      }
    ]
  },
  "modelVersion": "gemini-1.5-flash-latest",
  "responseId": "OnxBaKzbC8z3ld8P_N2LiA8"
}
提取的内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
=== 开始解析LLM响应 ===
原始内容长度: 464
原始内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
内容类型: string
清理后的内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
清理后的内容长度: 463
=== 开始解析结构化文本 ===
原始文本长度: 463
原始文本内容: "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"
分割后的行数: 16
处理第1行: {original: '"日期: 2025-05-30"', trimmed: '"日期: 2025-05-30"', length: 14}
找到冒号分隔符 ":" 在位置 2
提取键值对: {key: '"日期"', value: '"2025-05-30"'}
字段映射成功: 日期 -> date = 2025-05-30
处理第2行: {original: '"时间: 11:40"', trimmed: '"时间: 11:40"', length: 9}
找到冒号分隔符 ":" 在位置 2
提取键值对: {key: '"时间"', value: '"11:40"'}
字段映射成功: 时间 -> time = 11:40
处理第3行: {original: '"姓名: 王倩"', trimmed: '"姓名: 王倩"', length: 6}
找到冒号分隔符 ":" 在位置 2
提取键值对: {key: '"姓名"', value: '"王倩"'}
字段映射成功: 姓名 -> customer_name = 王倩
处理第4行: {original: '"航班: D7333"', trimmed: '"航班: D7333"', length: 9}
找到冒号分隔符 ":" 在位置 2
提取键值对: {key: '"航班"', value: '"D7333"'}
字段映射成功: 航班 -> flight_number = D7333
处理第5行: {original: '"pickup: klia"', trimmed: '"pickup: klia"', length: 12}
找到冒号分隔符 ":" 在位置 6
提取键值对: {key: '"pickup"', value: '"klia"'}
字段映射成功: pickup -> pickup_location = klia
处理第6行: {original: '"drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South"', trimmed: '"drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South"', length: 68}
找到冒号分隔符 ":" 在位置 4
提取键值对: {key: '"drop"', value: '"Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South"'}
字段映射成功: drop -> drop_location = Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
处理第7行: {original: '"service_type: pickup"', trimmed: '"service_type: pickup"', length: 20}
找到冒号分隔符 ":" 在位置 12
提取键值对: {key: '"service_type"', value: '"pickup"'}
字段映射成功: service_type -> service_type = pickup
处理第8行: {original: '"passenger_count: 2"', trimmed: '"passenger_count: 2"', length: 18}
找到冒号分隔符 ":" 在位置 15
提取键值对: {key: '"passenger_count"', value: '"2"'}
字段映射成功: passenger_count -> passenger_count = 2
处理第9行: {original: '"car_type: sedan"', trimmed: '"car_type: sedan"', length: 15}
找到冒号分隔符 ":" 在位置 8
提取键值对: {key: '"car_type"', value: '"sedan"'}
字段映射成功: car_type -> car_type = sedan
处理第10行: {original: '"car_type_id: 1"', trimmed: '"car_type_id: 1"', length: 14}
找到冒号分隔符 ":" 在位置 11
提取键值对: {key: '"car_type_id"', value: '"1"'}
字段映射成功: car_type_id -> car_type_id = 1
处理第11行: {original: '"ota_price: 69"', trimmed: '"ota_price: 69"', length: 13}
找到冒号分隔符 ":" 在位置 9
提取键值对: {key: '"ota_price"', value: '"69"'}
字段映射成功: ota_price -> ota_price = 69
处理第12行: {original: '"customer_contact: +60-XXXXXXXXX"', trimmed: '"customer_contact: +60-XXXXXXXXX"', length: 31}
找到冒号分隔符 ":" 在位置 16
提取键值对: {key: '"customer_contact"', value: '"+60-XXXXXXXXX"'}
字段映射成功: customer_contact -> customer_contact = +60-XXXXXXXXX
处理第13行: {original: '"customer_email: <EMAIL>"', trimmed: '"customer_email: <EMAIL>"', length: 43}
找到冒号分隔符 ":" 在位置 14
提取键值对: {key: '"customer_email"', value: '"<EMAIL>"'}
字段映射成功: customer_email -> customer_email = <EMAIL>
处理第14行: {original: '"driver_fee: 65"', trimmed: '"driver_fee: 65"', length: 14}
找到冒号分隔符 ":" 在位置 10
提取键值对: {key: '"driver_fee"', value: '"65"'}
字段映射成功: driver_fee -> driver_fee = 65
处理第15行: {original: '"extra_requirement: ⚠️请进入GMH Link Chat查询微信二维…服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"', trimmed: '"extra_requirement: ⚠️请进入GMH Link Chat查询微信二维…明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"', length: 114}
找到冒号分隔符 ":" 在位置 17
提取键值对: {key: '"extra_requirement"', value: '"⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群…说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"'}
字段映射成功: extra_requirement -> extra_requirement = ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
处理第16行: {original: '"other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"', trimmed: '"other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"', length: 48}
找到冒号分隔符 ":" 在位置 5
提取键值对: {key: '"other"', value: '"5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"'}
字段映射成功: other -> other = 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
=== 解析结构化文本完成 ===
解析后的订单数据: {date: '2025-05-30', time: '11:40', customer_name: '王倩', flight_number: 'D7333', pickup_location: 'klia', …}
解析出的字段数量: 16
parseStructuredText返回的数据: {date: '2025-05-30', time: '11:40', customer_name: '王倩', flight_number: 'D7333', pickup_location: 'klia', …}
orderData是否为对象: true
orderData键数量: 16
开始转换为标准订单格式...
=== 开始转换为标准订单格式 ===
输入的orderData: {date: '2025-05-30', time: '11:40', customer_name: '王倩', flight_number: 'D7333', pickup_location: 'klia', …}
orderData类型: object
orderData是否为null: false
orderData键列表: (16) ['date', 'time', 'customer_name', 'flight_number', 'pickup_location', 'drop_location', 'service_type', 'passenger_count', 'car_type', 'car_type_id', 'ota_price', 'customer_contact', 'customer_email', 'driver_fee', 'extra_requirement', 'other']
生成订单ID: ORDER_1749122106856_aag46f2sz
处理日期时间: {service_date: '2025-05-30', service_time: '11:40'}
组合后的日期时间: 2025-05-30 11:40
开始构建标准订单对象...
=== 标准订单对象构建完成 ===
标准订单对象: {id: 'ORDER_1749122106856_aag46f2sz', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
标准订单对象字段数量: 19
关键字段检查: {hasCustomerName: true, hasPickupLocation: true, hasDropLocation: true, hasServiceDate: true, hasServiceTime: true}
convertToStandardOrder返回的数据: {id: 'ORDER_1749122106856_aag46f2sz', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
成功解析订单: {id: 'ORDER_1749122106856_aag46f2sz', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
=== 解析完成 ===
解析出的订单数量: 1
最终返回结果: {rawContent: '日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
p…5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
', orders: Array(1), metadata: {…}}
解析结果: {
  "rawContent": "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer\n",
  "orders": [
    {
      "id": "ORDER_1749122106856_aag46f2sz",
      "customer_name": "王倩",
      "customer_contact": "+60-XXXXXXXXX",
      "customer_email": "<EMAIL>",
      "service_type": "pickup",
      "pickup_location": "klia",
      "drop_location": "Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South",
      "service_date": "2025-05-30",
      "service_time": "11:40",
      "order_datetime": "2025-05-30 11:40",
      "flight_number": "D7333",
      "passenger_count": 2,
      "car_type": "sedan",
      "ota_price": 69,
      "driver_fee": 65,
      "extra_requirement": "⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单",
      "other": "5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer",
      "created_at": "2025-06-05T11:15:06.856Z",
      "status": "pending"
    }
  ],
  "metadata": {
    "totalOrders": 1,
    "parseSuccess": true,
    "cleanedContent": "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer",
    "orderDataKeys": [
      "date",
      "time",
      "customer_name",
      "flight_number",
      "pickup_location",
      "drop_location",
      "service_type",
      "passenger_count",
      "car_type",
      "car_type_id",
      "ota_price",
      "customer_contact",
      "customer_email",
      "driver_fee",
      "extra_requirement",
      "other"
    ]
  }
}
=======================
[Gemini] API请求成功 {responseTime: 2922, contentLength: 464}
[LLM] Gemini API调用完成 {processingTime: 2923, success: true, error: null}
[LLM] Gemini处理成功 {totalProcessingTime: 2924, apiCallTime: 2923, provider: 'gemini'}
[SmartSelection] 车型选择完成 {carTypeId: 5, carTypeName: 'Compact 5 Seater', method: 'passenger_count_exact', confidence: 0.95}
[SmartSelection] 服务类型选择完成 {subCategoryId: 7, subCategoryName: 'Pickup', method: 'simplified_mapping', confidence: 0.9}
[SmartSelection] 后台用户选择完成 {userId: 3, userName: 'Operator', method: 'flight_service', confidence: 0.8}
[SmartSelection] 智能选择完成 {source: 'llm', processingTime: 11, successfulSelections: 3, fallbacks: 0}
[订单解析] 智能选择已应用到LLM解析结果 {originalOrder: {…}, enhancedOrder: {…}}
[订单解析] 订单解析完成 {otaType: 'chong-dealer', orderCount: 1, processingTime: '2938ms', success: true}
[UI] 显示订单结果 {orderCount: 1, otaType: 'chong-dealer', showManualEdit: false}
[应用] 订单处理完成 {orderCount: 1}
[UI] 隐藏加载状态 null
[订单] 开始创建订单流程 null
[API] 发起获取后端用户请求 null
[API响应] GET https://staging.gomyhire.com.my/api/backend_users - 200  {type: 'API_RESPONSE', method: 'GET', url: 'https://staging.gomyhire.com.my/api/backend_users', status: 200, timestamp: '2025-06-05T11:15:11.375Z', …}
[AppState] 用户数据缓存已更新 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', key: 'backendUsers', dataSize: 756}
[API] 获取后端用户成功 {userCount: 12, userNames: Array(5)}
[DataConsistency] 数据一致性验证完成 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', completeness: true, freshness: true, association: true, isValid: true}
[UI] 显示加载状态 {message: '正在创建订单...'}
[订单] 准备创建 1 个订单 null
[订单] 正在处理第 1 个订单 {id: 'ORDER_1749122106856_aag46f2sz', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
[API] 发起创建订单请求 {hasData: true, dataKeys: Array(23)}
[API请求] POST https://staging.gomyhire.com.my/api/create_order {type: 'API_REQUEST', method: 'POST', url: 'https://staging.gomyhire.com.my/api/create_order', timestamp: '2025-06-05T11:15:11.380Z', headers: {…}, …}
[API响应] POST https://staging.gomyhire.com.my/api/create_order - 200 (131ms) {type: 'API_RESPONSE', method: 'POST', url: 'https://staging.gomyhire.com.my/api/create_order', status: 200, timestamp: '2025-06-05T11:15:11.511Z', …}
[API] 创建订单成功 {responseTime: '131ms', orderId: 'unknown'}
[订单] 第 1 个订单创建成功 {status: false, message: 'Data need to be refined', data: {…}}
[UI] 隐藏加载状态 null
[UI] 显示创建结果 {successCount: 1, recoveredCount: 0, totalCount: 1}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1694ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1536ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1488ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1702ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1479ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[SmartSelection] API数据同步开始 null
DynamicApiSync 开始API数据同步
DynamicApiSync 模拟获取后台用户数据
DynamicApiSync 模拟获取子分类数据
DynamicApiSync 模拟获取车型数据
DynamicApiSync API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13, timestamp: '2025-06-05T11:44:53.721Z'}
[SmartSelection] API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13}
[SmartSelection] 应用状态不可用，使用默认映射 null
[Gemini] Gemini API连接成功 {responseTime: '1469ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1683ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1706ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1458ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1705ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1717ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[SmartSelection] API数据同步开始 null
DynamicApiSync 开始API数据同步
DynamicApiSync 模拟获取后台用户数据
DynamicApiSync 模拟获取子分类数据
DynamicApiSync 模拟获取车型数据
DynamicApiSync API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13, timestamp: '2025-06-05T12:14:53.722Z'}
[SmartSelection] API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13}
[SmartSelection] 应用状态不可用，使用默认映射 null
[Gemini] Gemini API连接成功 {responseTime: '1924ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1463ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1690ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1556ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1706ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1726ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[SmartSelection] API数据同步开始 null
DynamicApiSync 开始API数据同步
DynamicApiSync 模拟获取后台用户数据
DynamicApiSync 模拟获取子分类数据
DynamicApiSync 模拟获取车型数据
DynamicApiSync API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13, timestamp: '2025-06-05T12:44:53.727Z'}
[SmartSelection] API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13}
[SmartSelection] 应用状态不可用，使用默认映射 null
[Gemini] Gemini API连接成功 {responseTime: '1659ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1739ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[SmartSelection] API数据同步开始 null
DynamicApiSync 开始API数据同步
DynamicApiSync 模拟获取后台用户数据
DynamicApiSync 模拟获取子分类数据
DynamicApiSync 模拟获取车型数据
DynamicApiSync API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13, timestamp: '2025-06-05T15:21:43.426Z'}
[SmartSelection] API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13}
[SmartSelection] 应用状态不可用，使用默认映射 null
[Gemini] Gemini API连接检测失败 {error: 'Failed to fetch', consecutiveFailures: 1}
[UI] 更新LLM状态UI {gemini: false, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'disconnected', consecutiveFailures: 1}
[Gemini] Gemini API连接成功 {responseTime: '1711ms', statusChanged: true}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1690ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1691ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'connected', consecutiveFailures: 0}
[Gemini] Gemini API连接成功 {responseTime: '1676ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[AddressSearch] 地址搜索服务初始化 {isConfigured: true, apiKeyPresent: true}
Google Maps JavaScript API has been loaded directly without loading=async. This can result in suboptimal performance. For best-practice loading patterns please see https://goo.gle/js-api-loading
通知系统初始化完成
[AppState] 用户系统数据加载完成 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', loadedCount: 3, totalKeys: 3}
[AppState] 用户数据初始化完成 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', userEmail: '<EMAIL>'}
[应用] 开始初始化OTA订单处理系统 null
[UI] 更新连接状态 null
[UI] 文件上传初始化完成 null
[应用] UI组件初始化完成 null
[应用] 事件监听器绑定完成 null
[应用] 发现已保存的认证令牌，验证有效性 null
[API] 发起获取后端用户请求 null
[API响应] GET https://staging.gomyhire.com.my/api/backend_users - 200  {type: 'API_RESPONSE', method: 'GET', url: 'https://staging.gomyhire.com.my/api/backend_users', status: 200, timestamp: '2025-06-05T15:42:08.877Z', …}
[AppState] 用户数据缓存已更新 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', key: 'backendUsers', dataSize: 756}
[API] 获取后端用户成功 {userCount: 12, userNames: Array(5)}
[应用] 认证令牌有效 null
[UI] 隐藏登录模态框 null
[UI] 显示主界面 null
[应用] 检测LLM服务连接状态 null
[Gemini] 开始检测Gemini API连接状态 {previousStatus: 'checking', consecutiveFailures: 0}
[应用] 加载系统数据 null
[API] 发起获取后端用户请求 null
[API] 发起获取子分类请求 null
[API] 发起获取车型请求 null
[API响应] GET https://staging.gomyhire.com.my/api/backend_users - 200  {type: 'API_RESPONSE', method: 'GET', url: 'https://staging.gomyhire.com.my/api/backend_users', status: 200, timestamp: '2025-06-05T15:42:09.022Z', …}
[AppState] 用户数据缓存已更新 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', key: 'backendUsers', dataSize: 756}
[API] 获取后端用户成功 {userCount: 12, userNames: Array(5)}
[API响应] GET https://staging.gomyhire.com.my/api/sub_category - 200  {type: 'API_RESPONSE', method: 'GET', url: 'https://staging.gomyhire.com.my/api/sub_category', status: 200, timestamp: '2025-06-05T15:42:09.035Z', …}
[AppState] 用户数据缓存已更新 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', key: 'subCategories', dataSize: 4254}
[API] 获取子分类成功 {categoryCount: 11, categoryNames: Array(5)}
[API响应] GET https://staging.gomyhire.com.my/api/car_types - 200  {type: 'API_RESPONSE', method: 'GET', url: 'https://staging.gomyhire.com.my/api/car_types', status: 200, timestamp: '2025-06-05T15:42:09.042Z', …}
[AppState] 用户数据缓存已更新 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', key: 'carTypes', dataSize: 839}
[API] 获取车型成功 {carTypeCount: 13, carTypeNames: Array(5)}
[应用] 服务类型选择器保持简化版本（pickup/dropoff/charter） null
[UI] UI选择器更新完成 null
[应用] 系统数据加载完成 null
DynamicApiSync 开始API数据同步
DynamicApiSync 模拟获取后台用户数据
DynamicApiSync 模拟获取子分类数据
DynamicApiSync 模拟获取车型数据
DynamicApiSync 自动同步机制已启动 {interval: 1800000, endpoints: Array(3)}
[SmartSelection] 初始化智能选择服务 null
[SmartSelection] 应用状态不可用，使用默认映射 null
[应用] 智能选择服务初始化完成 null
[地址搜索] 地址搜索服务初始化完成 {isConfigured: true, cacheSize: 0, activeRequests: 0, lastConfigCheck: 1749138129064}
[应用] 系统初始化完成 null
[SmartSelection] 智能选择服务初始化完成 null
DynamicApiSync API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13, timestamp: '2025-06-05T15:42:09.068Z'}
[SmartSelection] API数据同步成功 {backendUsers: 12, subCategories: 4, carTypes: 13}
[SmartSelection] 应用状态不可用，使用默认映射 null
[Gemini] Gemini API连接成功 {responseTime: '1726ms', statusChanged: false}
[UI] 更新LLM状态UI {gemini: true, geminiElements: {…}}
[应用] 开始处理订单 {textLength: 116, otaType: 'auto'}
[UI] 显示加载状态 {message: '正在处理订单...'}
[订单解析] 开始解析订单 {textLength: 116, specifiedOtaType: 'auto'}
[订单解析] OTA类型检测完成 {detectedType: 'chong-dealer', confidence: 0.25}
[订单解析] 使用LLM解析订单 {otaType: 'chong-dealer'}
[LLM] 开始处理订单文本 {textLength: 116, otaType: 'chong-dealer'}
[LLM] 开始调用Gemini API null
=== Gemini 调试信息 ===
订单文本: 5月30日 接机: D7333 11:40抵达 


联系人：王倩
人数：2
车型：经济五座
酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South

Jy
OTA类型: chong-dealer
完整Prompt: 你是一个专业的订单处理助手。请根据以下需求，对"【订单列表】"中的每条订单信息进行处理。

⚠️ **重要说明**：你只需要专注于订单内容的解析和格式化，不需要进行OTA类型识别。

------------------------------------------------
【需求说明】

1. **多重日期判断与修正：**  
   - 以当前日期（2025-06-05）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。  
   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。  
   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。  
   - 在计算日期时，可按照以下优先级多次验证：  
     1. 如本月尚有同日并且未来可用，则使用本月该日；  
     2. 若本月该日已过或不合理，则顺延到下个月的同一天；  
     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。  

2. **时间计算：**  
   - **接机 (pickup)**：  
     - 使用航班"到达时间"作为 `订单时间`。  
   - **送机 (dropoff)**：  
     - 在航班"起飞时间"的基础上**减去 3.5 小时**，将结果作为 `订单时间`。  
   - 若原始时间无法直接解析，请在 `other` 字段中注明原始信息，并根据最近逻辑为 `订单时间` 赋合理数值。

3. **酒店名称：**  
   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；  
   - 若已是英文，且与实际查询结果一致，则原样保留；  
   - 如有疑问，请参考以下网站：  
     - https://hotels.ctrip.com  
     - https://booking.com  
     - https://google.com  

4. **上下车地点逻辑：**  
   - **接机 (pickup)**：`pickup = klia`, `drop = <英文酒店名>`  
   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`  
   - 任何备注或原始时间信息等，统一放入 `other` 字段中。

5. **专用格式要求：**
   - **时间格式**：采用简短格式 HH:MM（如：14:30）
   - **来源渠道**：在末端增加来源渠道标识 "- Chong Dealer"
   - **联系方式**：与专用格式相同，格式为 "+60-XXXXXXXXX"

6. **默认值设置：**
   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）
   - **customer_contact**：与专用格式相同
   - **customer_email**：<EMAIL>
   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）
   - **extra_requirement**："⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"

7. **最终结果：**  
   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。
   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。
   - 结果应为2025年
   - **服务类型判断**：只需判断三种基本类型
     - pickup：接机服务（从机场接客人到酒店/目的地）
     - dropoff：送机服务（从酒店/起点送客人到机场）
     - charter：包车服务（非机场相关的包车、一日游、点对点等）
   - **返回以下字段**：
     ```
     日期: YYYY-MM-DD
     时间: HH:MM-CD
     姓名: [客人姓名]
     航班: [航班号]
     pickup: [上车地点]
     drop: [下车地点]
     service_type: [pickup/dropoff/charter三选一]
     passenger_count: [乘客人数，默认1]
     car_type: [车型：sedan/mpv/van，智能判断]
     car_type_id: [车型ID：sedan=1, mpv=2, van=3]
     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]
     customer_contact: [联系电话]
     customer_email: <EMAIL>
     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]
     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
     other: [其他信息 - Chong Dealer]
     ```

------------------------------------------------
【订单列表】
5月30日 接机: D7333 11:40抵达 


联系人：王倩
人数：2
车型：经济五座
酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South

Jy

请严格按照以上要求处理订单信息，包含所有必要字段和默认值。
请求体: {
  "contents": [
    {
      "parts": [
        {
          "text": "你是一个专业的订单处理助手。请根据以下需求，对\"【订单列表】\"中的每条订单信息进行处理。\n\n⚠️ **重要说明**：你只需要专注于订单内容的解析和格式化，不需要进行OTA类型识别。\n\n------------------------------------------------\n【需求说明】\n\n1. **多重日期判断与修正：**  \n   - 以当前日期（2025-06-05）为锚点，对订单所填写的日期进行多次验算与推断，确保**最终输出的日期**为**最近且合理的未来日期**。  \n   - 若发现订单日期已过或与当前日期相比不再合理，则应依次顺延，直至找到**最接近原始日期**但仍位于未来的日期。  \n   - 若订单日期刚好是今天，或尚未到达，且**逻辑上可行**，则可使用原始日期。  \n   - 在计算日期时，可按照以下优先级多次验证：  \n     1. 如本月尚有同日并且未来可用，则使用本月该日；  \n     2. 若本月该日已过或不合理，则顺延到下个月的同一天；  \n     3. 若遇到跨年或当月无对应天数等特殊情况，则顺延至下一个最合理日期（例如 2 月无 30 号或 31 号时，需顺延到最近可行日期）。  \n\n2. **时间计算：**  \n   - **接机 (pickup)**：  \n     - 使用航班\"到达时间\"作为 `订单时间`。  \n   - **送机 (dropoff)**：  \n     - 在航班\"起飞时间\"的基础上**减去 3.5 小时**，将结果作为 `订单时间`。  \n   - 若原始时间无法直接解析，请在 `other` 字段中注明原始信息，并根据最近逻辑为 `订单时间` 赋合理数值。\n\n3. **酒店名称：**  \n   - 若酒店名是中文，请查询后替换为正确且标准的**英文名称**；  \n   - 若已是英文，且与实际查询结果一致，则原样保留；  \n   - 如有疑问，请参考以下网站：  \n     - https://hotels.ctrip.com  \n     - https://booking.com  \n     - https://google.com  \n\n4. **上下车地点逻辑：**  \n   - **接机 (pickup)**：`pickup = klia`, `drop = <英文酒店名>`  \n   - **送机 (dropoff)**：`pickup = <英文酒店名>`, `drop = klia`  \n   - 任何备注或原始时间信息等，统一放入 `other` 字段中。\n\n5. **专用格式要求：**\n   - **时间格式**：采用简短格式 HH:MM（如：14:30）\n   - **来源渠道**：在末端增加来源渠道标识 \"- Chong Dealer\"\n   - **联系方式**：与专用格式相同，格式为 \"+60-XXXXXXXXX\"\n\n6. **默认值设置：**\n   - **ota_price**：sedan 69, mpv 80（订单时间在 23:00-07:00 则增加 10）\n   - **customer_contact**：与专用格式相同\n   - **customer_email**：<EMAIL>\n   - **driver_fee**：sedan 65, mpv 75（订单时间在 23:00-07:00 则增加 10）\n   - **extra_requirement**：\"⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\"\n\n7. **最终结果：**  \n   - 为避免歧义，请对**每条订单**都进行**多次日期逻辑检查**，若校验通过则输出修正后日期；若无法确定，请在 `other` 中说明原始信息和推断过程。\n   - 输出必须为**纯文本**，不可添加多余解释或 markdown 语法。\n   - 结果应为2025年\n   - **服务类型判断**：只需判断三种基本类型\n     - pickup：接机服务（从机场接客人到酒店/目的地）\n     - dropoff：送机服务（从酒店/起点送客人到机场）\n     - charter：包车服务（非机场相关的包车、一日游、点对点等）\n   - **返回以下字段**：\n     ```\n     日期: YYYY-MM-DD\n     时间: HH:MM-CD\n     姓名: [客人姓名]\n     航班: [航班号]\n     pickup: [上车地点]\n     drop: [下车地点]\n     service_type: [pickup/dropoff/charter三选一]\n     passenger_count: [乘客人数，默认1]\n     car_type: [车型：sedan/mpv/van，智能判断]\n     car_type_id: [车型ID：sedan=1, mpv=2, van=3]\n     ota_price: [sedan 69, mpv 80, van 120，23:00-07:00时段+10]\n     customer_contact: [联系电话]\n     customer_email: <EMAIL>\n     driver_fee: [sedan 65, mpv 75, van 100，23:00-07:00时段+10]\n     extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\n     other: [其他信息 - Chong Dealer]\n     ```\n\n------------------------------------------------\n【订单列表】\n5月30日 接机: D7333 11:40抵达 \n\n\n联系人：王倩\n人数：2\n车型：经济五座\n酒店：Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\n\nJy\n\n请严格按照以上要求处理订单信息，包含所有必要字段和默认值。"
        }
      ]
    }
  ],
  "generationConfig": {
    "temperature": 0.1,
    "topK": 40,
    "topP": 0.95,
    "maxOutputTokens": 2048
  }
}
=======================
[Gemini] 发送API请求 {textLength: 116, otaType: 'chong-dealer', timeout: 30000}
=== Gemini 响应信息 ===
原始JSON响应: {
  "candidates": [
    {
      "content": {
        "parts": [
          {
            "text": "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer\n"
          }
        ],
        "role": "model"
      },
      "finishReason": "STOP",
      "avgLogprobs": -0.005301601418824953
    }
  ],
  "usageMetadata": {
    "promptTokenCount": 1449,
    "candidatesTokenCount": 214,
    "totalTokenCount": 1663,
    "promptTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 1449
      }
    ],
    "candidatesTokensDetails": [
      {
        "modality": "TEXT",
        "tokenCount": 214
      }
    ]
  },
  "modelVersion": "gemini-1.5-flash-latest",
  "responseId": "7rpBaNqqGuPPnvgPkLyl8Qg"
}
提取的内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
=== 开始解析LLM响应 ===
原始内容长度: 464
原始内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
内容类型: string
清理后的内容: 日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
pickup: klia
drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
service_type: pickup
passenger_count: 2
car_type: sedan
car_type_id: 1
ota_price: 69
customer_contact: +60-XXXXXXXXX
customer_email: <EMAIL>
driver_fee: 65
extra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
清理后的内容长度: 463
=== 开始解析结构化文本 ===
原始文本长度: 463
原始文本内容: "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"
分割后的行数: 16
处理第1行: {original: '"日期: 2025-05-30"', trimmed: '"日期: 2025-05-30"', length: 14}
找到冒号分隔符 ":" 在位置 2
提取键值对: {key: '"日期"', value: '"2025-05-30"'}
字段映射成功: 日期 -> date = 2025-05-30
处理第2行: {original: '"时间: 11:40"', trimmed: '"时间: 11:40"', length: 9}
找到冒号分隔符 ":" 在位置 2
提取键值对: {key: '"时间"', value: '"11:40"'}
字段映射成功: 时间 -> time = 11:40
处理第3行: {original: '"姓名: 王倩"', trimmed: '"姓名: 王倩"', length: 6}
找到冒号分隔符 ":" 在位置 2
提取键值对: {key: '"姓名"', value: '"王倩"'}
字段映射成功: 姓名 -> customer_name = 王倩
处理第4行: {original: '"航班: D7333"', trimmed: '"航班: D7333"', length: 9}
找到冒号分隔符 ":" 在位置 2
提取键值对: {key: '"航班"', value: '"D7333"'}
字段映射成功: 航班 -> flight_number = D7333
处理第5行: {original: '"pickup: klia"', trimmed: '"pickup: klia"', length: 12}
找到冒号分隔符 ":" 在位置 6
提取键值对: {key: '"pickup"', value: '"klia"'}
字段映射成功: pickup -> pickup_location = klia
处理第6行: {original: '"drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South"', trimmed: '"drop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South"', length: 68}
找到冒号分隔符 ":" 在位置 4
提取键值对: {key: '"drop"', value: '"Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South"'}
字段映射成功: drop -> drop_location = Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South
处理第7行: {original: '"service_type: pickup"', trimmed: '"service_type: pickup"', length: 20}
找到冒号分隔符 ":" 在位置 12
提取键值对: {key: '"service_type"', value: '"pickup"'}
字段映射成功: service_type -> service_type = pickup
处理第8行: {original: '"passenger_count: 2"', trimmed: '"passenger_count: 2"', length: 18}
找到冒号分隔符 ":" 在位置 15
提取键值对: {key: '"passenger_count"', value: '"2"'}
字段映射成功: passenger_count -> passenger_count = 2
处理第9行: {original: '"car_type: sedan"', trimmed: '"car_type: sedan"', length: 15}
找到冒号分隔符 ":" 在位置 8
提取键值对: {key: '"car_type"', value: '"sedan"'}
字段映射成功: car_type -> car_type = sedan
处理第10行: {original: '"car_type_id: 1"', trimmed: '"car_type_id: 1"', length: 14}
找到冒号分隔符 ":" 在位置 11
提取键值对: {key: '"car_type_id"', value: '"1"'}
字段映射成功: car_type_id -> car_type_id = 1
处理第11行: {original: '"ota_price: 69"', trimmed: '"ota_price: 69"', length: 13}
找到冒号分隔符 ":" 在位置 9
提取键值对: {key: '"ota_price"', value: '"69"'}
字段映射成功: ota_price -> ota_price = 69
处理第12行: {original: '"customer_contact: +60-XXXXXXXXX"', trimmed: '"customer_contact: +60-XXXXXXXXX"', length: 31}
找到冒号分隔符 ":" 在位置 16
提取键值对: {key: '"customer_contact"', value: '"+60-XXXXXXXXX"'}
字段映射成功: customer_contact -> customer_contact = +60-XXXXXXXXX
处理第13行: {original: '"customer_email: <EMAIL>"', trimmed: '"customer_email: <EMAIL>"', length: 43}
找到冒号分隔符 ":" 在位置 14
提取键值对: {key: '"customer_email"', value: '"<EMAIL>"'}
字段映射成功: customer_email -> customer_email = <EMAIL>
处理第14行: {original: '"driver_fee: 65"', trimmed: '"driver_fee: 65"', length: 14}
找到冒号分隔符 ":" 在位置 10
提取键值对: {key: '"driver_fee"', value: '"65"'}
字段映射成功: driver_fee -> driver_fee = 65
处理第15行: {original: '"extra_requirement: ⚠️请进入GMH Link Chat查询微信二维…服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"', trimmed: '"extra_requirement: ⚠️请进入GMH Link Chat查询微信二维…明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"', length: 114}
找到冒号分隔符 ":" 在位置 17
提取键值对: {key: '"extra_requirement"', value: '"⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群…说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单"'}
字段映射成功: extra_requirement -> extra_requirement = ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单
处理第16行: {original: '"other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"', trimmed: '"other: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"', length: 48}
找到冒号分隔符 ":" 在位置 5
提取键值对: {key: '"other"', value: '"5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer"'}
字段映射成功: other -> other = 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
=== 解析结构化文本完成 ===
解析后的订单数据: {date: '2025-05-30', time: '11:40', customer_name: '王倩', flight_number: 'D7333', pickup_location: 'klia', …}
解析出的字段数量: 16
parseStructuredText返回的数据: {date: '2025-05-30', time: '11:40', customer_name: '王倩', flight_number: 'D7333', pickup_location: 'klia', …}
orderData是否为对象: true
orderData键数量: 16
开始转换为标准订单格式...
=== 开始转换为标准订单格式 ===
输入的orderData: {date: '2025-05-30', time: '11:40', customer_name: '王倩', flight_number: 'D7333', pickup_location: 'klia', …}
orderData类型: object
orderData是否为null: false
orderData键列表: (16) ['date', 'time', 'customer_name', 'flight_number', 'pickup_location', 'drop_location', 'service_type', 'passenger_count', 'car_type', 'car_type_id', 'ota_price', 'customer_contact', 'customer_email', 'driver_fee', 'extra_requirement', 'other']
生成订单ID: ORDER_1749138158522_zi0r93oj6
处理日期时间: {service_date: '2025-05-30', service_time: '11:40'}
组合后的日期时间: 2025-05-30 11:40
开始构建标准订单对象...
=== 标准订单对象构建完成 ===
标准订单对象: {id: 'ORDER_1749138158522_zi0r93oj6', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
标准订单对象字段数量: 19
关键字段检查: {hasCustomerName: true, hasPickupLocation: true, hasDropLocation: true, hasServiceDate: true, hasServiceTime: true}
convertToStandardOrder返回的数据: {id: 'ORDER_1749138158522_zi0r93oj6', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
成功解析订单: {id: 'ORDER_1749138158522_zi0r93oj6', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
=== 解析完成 ===
解析出的订单数量: 1
最终返回结果: {rawContent: '日期: 2025-05-30
时间: 11:40
姓名: 王倩
航班: D7333
p…5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer
', orders: Array(1), metadata: {…}}
解析结果: {
  "rawContent": "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer\n",
  "orders": [
    {
      "id": "ORDER_1749138158522_zi0r93oj6",
      "customer_name": "王倩",
      "customer_contact": "+60-XXXXXXXXX",
      "customer_email": "<EMAIL>",
      "service_type": "pickup",
      "pickup_location": "klia",
      "drop_location": "Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South",
      "service_date": "2025-05-30",
      "service_time": "11:40",
      "order_datetime": "2025-05-30 11:40",
      "flight_number": "D7333",
      "passenger_count": 2,
      "car_type": "sedan",
      "ota_price": 69,
      "driver_fee": 65,
      "extra_requirement": "⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单",
      "other": "5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer",
      "created_at": "2025-06-05T15:42:38.523Z",
      "status": "pending"
    }
  ],
  "metadata": {
    "totalOrders": 1,
    "parseSuccess": true,
    "cleanedContent": "日期: 2025-05-30\n时间: 11:40\n姓名: 王倩\n航班: D7333\npickup: klia\ndrop: Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South\nservice_type: pickup\npassenger_count: 2\ncar_type: sedan\ncar_type_id: 1\nota_price: 69\ncustomer_contact: +60-XXXXXXXXX\ncustomer_email: <EMAIL>\ndriver_fee: 65\nextra_requirement: ⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单\nother: 5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer",
    "orderDataKeys": [
      "date",
      "time",
      "customer_name",
      "flight_number",
      "pickup_location",
      "drop_location",
      "service_type",
      "passenger_count",
      "car_type",
      "car_type_id",
      "ota_price",
      "customer_contact",
      "customer_email",
      "driver_fee",
      "extra_requirement",
      "other"
    ]
  }
}
=======================
[Gemini] API请求成功 {responseTime: 2934, contentLength: 464}
[LLM] Gemini API调用完成 {processingTime: 2937, success: true, error: null}
[LLM] Gemini处理成功 {totalProcessingTime: 2940, apiCallTime: 2937, provider: 'gemini'}
[SmartSelection] 车型选择完成 {carTypeId: 5, carTypeName: 'Compact 5 Seater', method: 'passenger_count_exact', confidence: 0.95}
[SmartSelection] 服务类型选择完成 {subCategoryId: 7, subCategoryName: 'Pickup', method: 'simplified_mapping', confidence: 0.9}
[SmartSelection] 后台用户选择完成 {userId: 3, userName: 'Operator', method: 'flight_service', confidence: 0.8}
[SmartSelection] 智能选择完成 {source: 'llm', processingTime: 14, successfulSelections: 3, fallbacks: 0}
[订单解析] 智能选择已应用到LLM解析结果 {originalOrder: {…}, enhancedOrder: {…}}
[订单解析] 订单解析完成 {otaType: 'chong-dealer', orderCount: 1, processingTime: '2964ms', success: true}
[UI] 显示订单结果 {orderCount: 1, otaType: 'chong-dealer', showManualEdit: false}
[应用] 订单处理完成 {orderCount: 1}
[UI] 隐藏加载状态 null
[订单] 开始创建订单流程 null
[API] 发起获取后端用户请求 null
[API响应] GET https://staging.gomyhire.com.my/api/backend_users - 200  {type: 'API_RESPONSE', method: 'GET', url: 'https://staging.gomyhire.com.my/api/backend_users', status: 200, timestamp: '2025-06-05T15:42:51.425Z', …}
[AppState] 用户数据缓存已更新 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', key: 'backendUsers', dataSize: 756}
[API] 获取后端用户成功 {userCount: 12, userNames: Array(5)}
[DataConsistency] 数据一致性验证完成 {userHash: 'c3VwZXJfYWRtaW5AZ29teWhpcmUuY29tX2Fub255bW91cw', completeness: true, freshness: true, association: true, isValid: true}
[UI] 显示加载状态 {message: '正在创建订单...'}
[订单] 准备创建 1 个订单 null
[订单] 正在处理第 1 个订单 {id: 'ORDER_1749138158522_zi0r93oj6', customer_name: '王倩', customer_contact: '+60-XXXXXXXXX', customer_email: '<EMAIL>', service_type: 'pickup', …}
[API] 发起创建订单请求 {hasData: true, dataKeys: Array(23)}
🔍 DEBUG: Actual orderData being sent to API: {
  "id": "ORDER_1749138158522_zi0r93oj6",
  "customer_name": "王倩",
  "customer_contact": "+60-XXXXXXXXX",
  "customer_email": "<EMAIL>",
  "service_type": "pickup",
  "pickup_location": "klia",
  "drop_location": "Hilton Garden Inn Kuala Lumpur Jalan Tuanku Abdul Rahman South",
  "service_date": "2025-05-30",
  "service_time": "11:40",
  "order_datetime": "2025-05-30 11:40",
  "flight_number": "D7333",
  "passenger_count": 2,
  "car_type": "sedan",
  "ota_price": 69,
  "driver_fee": 65,
  "extra_requirement": "⚠️请进入GMH Link Chat查询微信二维码进群，扫码进群 发车子资料照片 登入群跟客人打招呼和说明服务性质 , 接单后1小时内看群里没二维码向GMH客服领 没处理1小时会自动取消订单",
  "other": "5月30日 接机: D7333 11:40抵达 Jy - Chong Dealer",
  "created_at": "2025-06-05T15:42:38.523Z",
  "status": "pending",
  "car_type_id": 5,
  "sub_category_id": 7,
  "incharge_by_backend_user_id": 3,
  "_smartSelection": {
    "source": "llm",
    "timestamp": "2025-06-05T15:42:38.528Z",
    "results": {
      "vehicle": {
        "id": 5,
        "name": "Compact 5 Seater",
        "reason": "基于乘客数量 2 的精确匹配 [历史推荐: 基于2个相似成功案例的分析]",
        "method": "passenger_count_exact"
      },
      "service": {
        "id": 7,
        "name": "Pickup",
        "reason": "接机服务",
        "method": "simplified_mapping"
      },
      "user": {
        "id": 3,
        "name": "Operator",
        "reason": "基于航班服务的时间敏感性分配操作员",
        "method": "flight_service"
      }
    },
    "confidence": {
      "vehicle": 0.95,
      "service": 0.9,
      "user": 0.8
    },
    "fallbacks": {},
    "processingTime": 14
  }
}
🔍 DEBUG: ota_reference_number value: undefined
🔍 DEBUG: All keys in orderData: (23) ['id', 'customer_name', 'customer_contact', 'customer_email', 'service_type', 'pickup_location', 'drop_location', 'service_date', 'service_time', 'order_datetime', 'flight_number', 'passenger_count', 'car_type', 'ota_price', 'driver_fee', 'extra_requirement', 'other', 'created_at', 'status', 'car_type_id', 'sub_category_id', 'incharge_by_backend_user_id', '_smartSelection']
[API请求] POST https://staging.gomyhire.com.my/api/create_order {type: 'API_REQUEST', method: 'POST', url: 'https://staging.gomyhire.com.my/api/create_order', timestamp: '2025-06-05T15:42:51.433Z', headers: {…}, …}
[API响应] POST https://staging.gomyhire.com.my/api/create_order - 200 (101ms) {type: 'API_RESPONSE', method: 'POST', url: 'https://staging.gomyhire.com.my/api/create_order', status: 200, timestamp: '2025-06-05T15:42:51.533Z', …}
[API] 创建订单成功 {responseTime: '101ms', orderId: 'unknown'}
[订单] 第 1 个订单创建成功 {status: false, message: 'Data need to be refined', data: {…}}
[UI] 隐藏加载状态 null
[UI] 显示创建结果 {successCount: 1, recoveredCount: 0, totalCount: 1}

```
